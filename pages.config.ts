import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'unibest',
    navigationBarBackgroundColor: '#f8f8f8',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  subPackages: [],
  preloadRule: {
    'pages/index/index': {
      network: 'all',
      packages: [],
    },
  },
  // entryPagePath: 'pages/loading/index',
  tabBar: {
    color: '#7F7F7F',
    selectedColor: '#1A1A1A',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    height: '50px',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/activity.png',
        selectedIconPath: 'static/tabbar/activityHL.png',
        pagePath: 'pages/participated/index',
        text: '活动',
      },
      {
        iconPath: 'static/tabbar/shequ.png',
        selectedIconPath: 'static/tabbar/shequHL.png',
        pagePath: 'pages/shequ/index',
        text: '社区',
      },
      {
        iconPath: 'static/tabbar/shequn.png',
        selectedIconPath: 'static/tabbar/shequnHL.png',
        pagePath: 'pages/shequn/index',
        text: '社群',
      },
      {
        iconPath: 'static/tabbar/personal.png',
        selectedIconPath: 'static/tabbar/personalHL.png',
        pagePath: 'pages/user/index',
        text: '我的',
      },
    ],
  },
})
